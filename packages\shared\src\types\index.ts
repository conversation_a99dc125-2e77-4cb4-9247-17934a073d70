// User types
export interface User {
  id: string
  email: string
  created_at: string
  updated_at: string
  profile_completed: boolean
  subscription_tier: 'free' | 'premium' | 'pro'
}

export interface UserProfile {
  id: string
  user_id: string
  first_name?: string
  last_name?: string
  date_of_birth?: string
  gender?: 'male' | 'female' | 'other' | 'prefer_not_to_say'
  height_cm?: number
  weight_kg?: number
  activity_level: 'sedentary' | 'lightly_active' | 'moderately_active' | 'very_active' | 'extremely_active'
  primary_goal: 'weight_loss' | 'muscle_gain' | 'maintenance' | 'endurance' | 'general_health'
  dietary_restrictions: string[]
  food_allergies: string[]
  fitness_experience: 'beginner' | 'intermediate' | 'advanced'
  available_equipment: string[]
  workout_preferences: string[]
  created_at: string
  updated_at: string
}

// Nutrition types
export interface Recipe {
  id: string
  name: string
  description?: string
  prep_time_minutes: number
  cook_time_minutes: number
  servings: number
  difficulty_level: 'easy' | 'medium' | 'hard'
  cuisine_type: string
  dietary_tags: string[]
  ingredients: Ingredient[]
  instructions: RecipeStep[]
  nutrition_per_serving: NutritionInfo
  image_url?: string
  created_at: string
}

export interface Ingredient {
  name: string
  amount: number
  unit: string
  notes?: string
}

export interface RecipeStep {
  step_number: number
  instruction: string
  duration_minutes?: number
}

export interface NutritionInfo {
  calories: number
  protein_g: number
  carbs_g: number
  fat_g: number
  fiber_g: number
  sugar_g: number
  sodium_mg: number
}

export interface MealPlan {
  id: string
  user_id: string
  name: string
  start_date: string
  end_date: string
  daily_calories: number
  macro_protein_percent: number
  macro_carb_percent: number
  macro_fat_percent: number
  is_active: boolean
  created_by_ai: boolean
  meals: MealPlanDay[]
  created_at: string
}

export interface MealPlanDay {
  date: string
  breakfast: Recipe[]
  lunch: Recipe[]
  dinner: Recipe[]
  snacks: Recipe[]
}

// Fitness types
export interface Exercise {
  id: string
  name: string
  description?: string
  muscle_groups: string[]
  equipment_required: string[]
  difficulty_level: 'beginner' | 'intermediate' | 'advanced'
  exercise_type: 'strength' | 'cardio' | 'flexibility' | 'balance' | 'plyometric'
  instructions: ExerciseInstruction[]
  video_url?: string
  thumbnail_url?: string
  safety_tips: string[]
  common_mistakes: string[]
  created_at: string
}

export interface ExerciseInstruction {
  step_number: number
  instruction: string
}

export interface WorkoutPlan {
  id: string
  user_id: string
  name: string
  description?: string
  duration_weeks: number
  difficulty_level: 'beginner' | 'intermediate' | 'advanced'
  primary_goal: string
  equipment_required: string[]
  is_active: boolean
  created_by_ai: boolean
  workouts: WorkoutDay[]
  created_at: string
}

export interface WorkoutDay {
  day_of_week: number // 0-6, Sunday = 0
  name: string
  exercises: WorkoutExercise[]
  estimated_duration_minutes: number
}

export interface WorkoutExercise {
  exercise_id: string
  exercise: Exercise
  sets: number
  reps?: number
  duration_seconds?: number
  weight_kg?: number
  rest_seconds: number
  notes?: string
}

// Progress tracking types
export interface ProgressEntry {
  id: string
  user_id: string
  metric_type: 'weight' | 'body_fat' | 'muscle_mass' | 'measurements' | 'performance'
  value: number
  unit: string
  recorded_date: string
  notes?: string
  created_at: string
}

// AI types
export interface AIRecommendation {
  id: string
  user_id: string
  recommendation_type: 'meal_plan' | 'workout_plan' | 'recipe' | 'exercise' | 'adjustment'
  content: Record<string, any>
  confidence_score: number
  model_version: string
  feedback_rating?: number
  was_accepted?: boolean
  created_at: string
}

// Grocery types
export interface GroceryList {
  id: string
  user_id: string
  meal_plan_id?: string
  name: string
  items: GroceryItem[]
  total_estimated_cost?: number
  store_preferences: string[]
  is_completed: boolean
  created_at: string
}

export interface GroceryItem {
  name: string
  quantity: number
  unit: string
  category: string
  estimated_cost?: number
  is_purchased: boolean
  notes?: string
}

// API Response types
export interface ApiResponse<T> {
  data: T
  success: boolean
  message?: string
}

export interface PaginatedResponse<T> {
  data: T[]
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
  }
}

// Error types
export interface ApiError {
  code: string
  message: string
  details?: Record<string, any>
}
