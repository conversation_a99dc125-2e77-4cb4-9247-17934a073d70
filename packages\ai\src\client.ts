import { GoogleGenerativeAI } from '@google/generative-ai'

// Initialize Gemini client
const apiKey = process.env.GOOGLE_GENERATIVE_AI_API_KEY

if (!apiKey) {
  throw new Error('GOOGLE_GENERATIVE_AI_API_KEY environment variable is required')
}

export const genAI = new GoogleGenerativeAI(apiKey)

// Model configurations
export const modelConfigs = {
  flash: {
    model: 'gemini-2.5-flash',
    generationConfig: {
      temperature: 0.7,
      topP: 0.8,
      topK: 40,
      maxOutputTokens: 8192,
    },
    safetySettings: [
      {
        category: 'HARM_CATEGORY_HARASSMENT',
        threshold: 'BLOCK_MEDIUM_AND_ABOVE',
      },
      {
        category: 'HARM_CATEGORY_HATE_SPEECH',
        threshold: 'BLOCK_MEDIUM_AND_ABOVE',
      },
      {
        category: 'HARM_CATEGORY_SEXUALLY_EXPLICIT',
        threshold: 'BLOCK_MEDIUM_AND_ABOVE',
      },
      {
        category: 'HARM_CATEGORY_DANGEROUS_CONTENT',
        threshold: 'BLOCK_MEDIUM_AND_ABOVE',
      },
    ],
  },
  pro: {
    model: 'gemini-2.5-pro',
    generationConfig: {
      temperature: 0.5,
      topP: 0.9,
      topK: 32,
      maxOutputTokens: 8192,
    },
    safetySettings: [
      {
        category: 'HARM_CATEGORY_HARASSMENT',
        threshold: 'BLOCK_MEDIUM_AND_ABOVE',
      },
      {
        category: 'HARM_CATEGORY_HATE_SPEECH',
        threshold: 'BLOCK_MEDIUM_AND_ABOVE',
      },
      {
        category: 'HARM_CATEGORY_SEXUALLY_EXPLICIT',
        threshold: 'BLOCK_MEDIUM_AND_ABOVE',
      },
      {
        category: 'HARM_CATEGORY_DANGEROUS_CONTENT',
        threshold: 'BLOCK_MEDIUM_AND_ABOVE',
      },
    ],
  },
}

// Get model instances
export const getFlashModel = () => genAI.getGenerativeModel(modelConfigs.flash)
export const getProModel = () => genAI.getGenerativeModel(modelConfigs.pro)

// Default model (Flash for most use cases)
export const getDefaultModel = () => getFlashModel()

// Model selection based on complexity
export const getModelForTask = (complexity: 'simple' | 'complex' = 'simple') => {
  return complexity === 'complex' ? getProModel() : getFlashModel()
}
