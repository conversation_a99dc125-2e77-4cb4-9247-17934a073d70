{"name": "@platemotion/shared", "version": "0.1.0", "private": true, "main": "./src/index.ts", "types": "./src/index.ts", "scripts": {"lint": "eslint . --max-warnings 0", "type-check": "tsc --noEmit", "test": "vitest"}, "dependencies": {"zod": "catalog:", "date-fns": "catalog:"}, "devDependencies": {"@platemotion/eslint-config": "workspace:*", "@platemotion/typescript-config": "workspace:*", "typescript": "catalog:", "vitest": "catalog:"}}