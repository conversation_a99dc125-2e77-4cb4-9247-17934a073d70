import { z } from 'zod'
import { 
  ACTIVITY_LEVELS, 
  FITNESS_GOALS, 
  FITNESS_EXPERIENCE_LEVELS,
  DIETARY_RESTRICTIONS,
  VALIDATION_RULES
} from '../constants'

// User schemas
export const userProfileSchema = z.object({
  first_name: z.string().max(VALIDATION_RULES.NAME_MAX_LENGTH).optional(),
  last_name: z.string().max(VALIDATION_RULES.NAME_MAX_LENGTH).optional(),
  date_of_birth: z.string().date().optional(),
  gender: z.enum(['male', 'female', 'other', 'prefer_not_to_say']).optional(),
  height_cm: z.number()
    .min(VALIDATION_RULES.MIN_HEIGHT_CM)
    .max(VALIDATION_RULES.MAX_HEIGHT_CM)
    .optional(),
  weight_kg: z.number()
    .min(VALIDATION_RULES.MIN_WEIGHT_KG)
    .max(VALIDATION_RULES.MAX_WEIGHT_KG)
    .optional(),
  activity_level: z.enum([
    ACTIVITY_LEVELS.SEDENTARY,
    ACTIVITY_LEVELS.LIGHTLY_ACTIVE,
    ACTIVITY_LEVELS.MODERATELY_ACTIVE,
    ACTIVITY_LEVELS.VERY_ACTIVE,
    ACTIVITY_LEVELS.EXTREMELY_ACTIVE
  ]),
  primary_goal: z.enum([
    FITNESS_GOALS.WEIGHT_LOSS,
    FITNESS_GOALS.MUSCLE_GAIN,
    FITNESS_GOALS.MAINTENANCE,
    FITNESS_GOALS.ENDURANCE,
    FITNESS_GOALS.GENERAL_HEALTH
  ]),
  dietary_restrictions: z.array(z.string()),
  food_allergies: z.array(z.string()),
  fitness_experience: z.enum([
    FITNESS_EXPERIENCE_LEVELS.BEGINNER,
    FITNESS_EXPERIENCE_LEVELS.INTERMEDIATE,
    FITNESS_EXPERIENCE_LEVELS.ADVANCED
  ]),
  available_equipment: z.array(z.string()),
  workout_preferences: z.array(z.string())
})

export const updateUserProfileSchema = userProfileSchema.partial()

// Authentication schemas
export const signUpSchema = z.object({
  email: z.string().email().max(VALIDATION_RULES.EMAIL_MAX_LENGTH),
  password: z.string().min(VALIDATION_RULES.PASSWORD_MIN_LENGTH),
  first_name: z.string().min(1).max(VALIDATION_RULES.NAME_MAX_LENGTH),
  last_name: z.string().min(1).max(VALIDATION_RULES.NAME_MAX_LENGTH)
})

export const signInSchema = z.object({
  email: z.string().email(),
  password: z.string().min(1)
})

export const resetPasswordSchema = z.object({
  email: z.string().email()
})

export const updatePasswordSchema = z.object({
  current_password: z.string().min(1),
  new_password: z.string().min(VALIDATION_RULES.PASSWORD_MIN_LENGTH)
})

// Recipe schemas
export const ingredientSchema = z.object({
  name: z.string().min(1),
  amount: z.number().positive(),
  unit: z.string().min(1),
  notes: z.string().optional()
})

export const recipeStepSchema = z.object({
  step_number: z.number().positive(),
  instruction: z.string().min(1),
  duration_minutes: z.number().positive().optional()
})

export const nutritionInfoSchema = z.object({
  calories: z.number().nonnegative(),
  protein_g: z.number().nonnegative(),
  carbs_g: z.number().nonnegative(),
  fat_g: z.number().nonnegative(),
  fiber_g: z.number().nonnegative(),
  sugar_g: z.number().nonnegative(),
  sodium_mg: z.number().nonnegative()
})

export const createRecipeSchema = z.object({
  name: z.string().min(1).max(255),
  description: z.string().max(VALIDATION_RULES.DESCRIPTION_MAX_LENGTH).optional(),
  prep_time_minutes: z.number().positive(),
  cook_time_minutes: z.number().nonnegative(),
  servings: z.number().positive(),
  difficulty_level: z.enum(['easy', 'medium', 'hard']),
  cuisine_type: z.string().min(1),
  dietary_tags: z.array(z.string()),
  ingredients: z.array(ingredientSchema).min(1),
  instructions: z.array(recipeStepSchema).min(1),
  nutrition_per_serving: nutritionInfoSchema,
  image_url: z.string().url().optional()
})

export const updateRecipeSchema = createRecipeSchema.partial()

// Exercise schemas
export const exerciseInstructionSchema = z.object({
  step_number: z.number().positive(),
  instruction: z.string().min(1)
})

export const createExerciseSchema = z.object({
  name: z.string().min(1).max(255),
  description: z.string().max(VALIDATION_RULES.DESCRIPTION_MAX_LENGTH).optional(),
  muscle_groups: z.array(z.string()).min(1),
  equipment_required: z.array(z.string()),
  difficulty_level: z.enum(['beginner', 'intermediate', 'advanced']),
  exercise_type: z.enum(['strength', 'cardio', 'flexibility', 'balance', 'plyometric']),
  instructions: z.array(exerciseInstructionSchema).min(1),
  video_url: z.string().url().optional(),
  thumbnail_url: z.string().url().optional(),
  safety_tips: z.array(z.string()),
  common_mistakes: z.array(z.string())
})

export const updateExerciseSchema = createExerciseSchema.partial()

// Workout schemas
export const workoutExerciseSchema = z.object({
  exercise_id: z.string().uuid(),
  sets: z.number().positive(),
  reps: z.number().positive().optional(),
  duration_seconds: z.number().positive().optional(),
  weight_kg: z.number().positive().optional(),
  rest_seconds: z.number().nonnegative(),
  notes: z.string().optional()
})

export const workoutDaySchema = z.object({
  day_of_week: z.number().min(0).max(6),
  name: z.string().min(1),
  exercises: z.array(workoutExerciseSchema).min(1),
  estimated_duration_minutes: z.number().positive()
})

export const createWorkoutPlanSchema = z.object({
  name: z.string().min(1).max(255),
  description: z.string().max(VALIDATION_RULES.DESCRIPTION_MAX_LENGTH).optional(),
  duration_weeks: z.number().positive(),
  difficulty_level: z.enum(['beginner', 'intermediate', 'advanced']),
  primary_goal: z.string().min(1),
  equipment_required: z.array(z.string()),
  workouts: z.array(workoutDaySchema).min(1)
})

export const updateWorkoutPlanSchema = createWorkoutPlanSchema.partial()

// Meal plan schemas
export const createMealPlanSchema = z.object({
  name: z.string().min(1).max(255),
  start_date: z.string().date(),
  end_date: z.string().date(),
  daily_calories: z.number().positive(),
  macro_protein_percent: z.number().min(0).max(100),
  macro_carb_percent: z.number().min(0).max(100),
  macro_fat_percent: z.number().min(0).max(100)
}).refine(
  (data) => data.macro_protein_percent + data.macro_carb_percent + data.macro_fat_percent === 100,
  {
    message: "Macro percentages must sum to 100",
    path: ["macro_protein_percent"]
  }
)

export const updateMealPlanSchema = createMealPlanSchema.partial()

// Progress tracking schemas
export const createProgressEntrySchema = z.object({
  metric_type: z.enum(['weight', 'body_fat', 'muscle_mass', 'measurements', 'performance']),
  value: z.number(),
  unit: z.string().min(1),
  recorded_date: z.string().date(),
  notes: z.string().optional()
})

export const updateProgressEntrySchema = createProgressEntrySchema.partial()

// Grocery list schemas
export const groceryItemSchema = z.object({
  name: z.string().min(1),
  quantity: z.number().positive(),
  unit: z.string().min(1),
  category: z.string().min(1),
  estimated_cost: z.number().nonnegative().optional(),
  is_purchased: z.boolean().default(false),
  notes: z.string().optional()
})

export const createGroceryListSchema = z.object({
  name: z.string().min(1).max(255),
  meal_plan_id: z.string().uuid().optional(),
  items: z.array(groceryItemSchema).min(1),
  store_preferences: z.array(z.string())
})

export const updateGroceryListSchema = createGroceryListSchema.partial()

// AI schemas
export const aiMealPlanRequestSchema = z.object({
  user_preferences: userProfileSchema,
  duration_days: z.number().positive().max(30),
  exclude_recipes: z.array(z.string().uuid()).optional(),
  special_requirements: z.string().optional()
})

export const aiWorkoutPlanRequestSchema = z.object({
  user_preferences: userProfileSchema,
  duration_weeks: z.number().positive().max(12),
  exclude_exercises: z.array(z.string().uuid()).optional(),
  special_requirements: z.string().optional()
})

export const aiFeedbackSchema = z.object({
  recommendation_id: z.string().uuid(),
  rating: z.number().min(1).max(5),
  feedback_text: z.string().optional(),
  was_helpful: z.boolean()
})

// Pagination schemas
export const paginationSchema = z.object({
  page: z.number().positive().default(1),
  limit: z.number().positive().max(100).default(20),
  sort_by: z.string().optional(),
  sort_order: z.enum(['asc', 'desc']).default('desc')
})

// Search schemas
export const searchSchema = z.object({
  query: z.string().min(1),
  filters: z.record(z.any()).optional(),
  ...paginationSchema.shape
})

// File upload schemas
export const fileUploadSchema = z.object({
  file_name: z.string().min(1),
  file_type: z.string().min(1),
  file_size: z.number().positive(),
  bucket: z.string().min(1)
})

// Notification schemas
export const createNotificationSchema = z.object({
  type: z.enum(['workout_reminder', 'meal_reminder', 'progress_update', 'achievement', 'system']),
  title: z.string().min(1),
  message: z.string().min(1),
  scheduled_for: z.string().datetime().optional(),
  data: z.record(z.any()).optional()
})

// Export type inference helpers
export type UserProfileInput = z.infer<typeof userProfileSchema>
export type UpdateUserProfileInput = z.infer<typeof updateUserProfileSchema>
export type SignUpInput = z.infer<typeof signUpSchema>
export type SignInInput = z.infer<typeof signInSchema>
export type CreateRecipeInput = z.infer<typeof createRecipeSchema>
export type UpdateRecipeInput = z.infer<typeof updateRecipeSchema>
export type CreateExerciseInput = z.infer<typeof createExerciseSchema>
export type UpdateExerciseInput = z.infer<typeof updateExerciseSchema>
export type CreateWorkoutPlanInput = z.infer<typeof createWorkoutPlanSchema>
export type UpdateWorkoutPlanInput = z.infer<typeof updateWorkoutPlanSchema>
export type CreateMealPlanInput = z.infer<typeof createMealPlanSchema>
export type UpdateMealPlanInput = z.infer<typeof updateMealPlanSchema>
export type CreateProgressEntryInput = z.infer<typeof createProgressEntrySchema>
export type UpdateProgressEntryInput = z.infer<typeof updateProgressEntrySchema>
export type CreateGroceryListInput = z.infer<typeof createGroceryListSchema>
export type UpdateGroceryListInput = z.infer<typeof updateGroceryListSchema>
export type AIMealPlanRequestInput = z.infer<typeof aiMealPlanRequestSchema>
export type AIWorkoutPlanRequestInput = z.infer<typeof aiWorkoutPlanRequestSchema>
export type AIFeedbackInput = z.infer<typeof aiFeedbackSchema>
export type PaginationInput = z.infer<typeof paginationSchema>
export type SearchInput = z.infer<typeof searchSchema>
export type FileUploadInput = z.infer<typeof fileUploadSchema>
export type CreateNotificationInput = z.infer<typeof createNotificationSchema>
