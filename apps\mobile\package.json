{"name": "@platemotion/mobile", "version": "0.1.0", "private": true, "main": "expo-router/entry", "scripts": {"start": "expo start", "dev": "expo start --dev-client", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "build": "expo export", "build:android": "eas build --platform android", "build:ios": "eas build --platform ios", "lint": "eslint . --max-warnings 0", "type-check": "tsc --noEmit", "test": "vitest"}, "dependencies": {"expo": "catalog:", "expo-router": "catalog:", "expo-status-bar": "~1.11.1", "expo-system-ui": "~2.9.3", "expo-web-browser": "~12.8.2", "expo-constants": "~15.4.5", "expo-linking": "~6.2.2", "expo-font": "~11.10.2", "expo-splash-screen": "~0.26.4", "expo-secure-store": "~12.8.1", "expo-image-picker": "~14.7.1", "expo-camera": "~14.1.3", "expo-av": "~13.10.4", "react": "catalog:", "react-native": "catalog:", "react-native-safe-area-context": "4.8.2", "react-native-screens": "~3.29.0", "react-native-gesture-handler": "~2.14.0", "react-native-reanimated": "~3.6.2", "nativewind": "catalog:", "zustand": "catalog:", "@supabase/supabase-js": "catalog:", "@supabase/auth-helpers-react": "catalog:", "@platemotion/shared": "workspace:*", "@platemotion/database": "workspace:*", "@platemotion/ai": "workspace:*", "@platemotion/ui": "workspace:*"}, "devDependencies": {"@babel/core": "^7.23.6", "@types/react": "catalog:", "@types/react-native": "^0.72.8", "@platemotion/eslint-config": "workspace:*", "@platemotion/typescript-config": "workspace:*", "typescript": "catalog:", "tailwindcss": "catalog:", "vitest": "catalog:"}}