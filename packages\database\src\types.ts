// This file will be auto-generated by Supabase CLI
// Run: supabase gen types typescript --local > packages/database/src/types.ts

export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export interface Database {
  public: {
    Tables: {
      users: {
        Row: {
          id: string
          email: string
          created_at: string
          updated_at: string
          profile_completed: boolean
          subscription_tier: string
        }
        Insert: {
          id?: string
          email: string
          created_at?: string
          updated_at?: string
          profile_completed?: boolean
          subscription_tier?: string
        }
        Update: {
          id?: string
          email?: string
          created_at?: string
          updated_at?: string
          profile_completed?: boolean
          subscription_tier?: string
        }
        Relationships: []
      }
      user_profiles: {
        Row: {
          id: string
          user_id: string
          first_name: string | null
          last_name: string | null
          date_of_birth: string | null
          gender: string | null
          height_cm: number | null
          weight_kg: number | null
          activity_level: string
          primary_goal: string
          dietary_restrictions: string[]
          food_allergies: string[]
          fitness_experience: string
          available_equipment: string[]
          workout_preferences: string[]
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          first_name?: string | null
          last_name?: string | null
          date_of_birth?: string | null
          gender?: string | null
          height_cm?: number | null
          weight_kg?: number | null
          activity_level: string
          primary_goal: string
          dietary_restrictions?: string[]
          food_allergies?: string[]
          fitness_experience: string
          available_equipment?: string[]
          workout_preferences?: string[]
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          first_name?: string | null
          last_name?: string | null
          date_of_birth?: string | null
          gender?: string | null
          height_cm?: number | null
          weight_kg?: number | null
          activity_level?: string
          primary_goal?: string
          dietary_restrictions?: string[]
          food_allergies?: string[]
          fitness_experience?: string
          available_equipment?: string[]
          workout_preferences?: string[]
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "user_profiles_user_id_fkey"
            columns: ["user_id"]
            referencedRelation: "users"
            referencedColumns: ["id"]
          }
        ]
      }
      recipes: {
        Row: {
          id: string
          name: string
          description: string | null
          prep_time_minutes: number
          cook_time_minutes: number
          servings: number
          difficulty_level: string
          cuisine_type: string
          dietary_tags: string[]
          ingredients: Json
          instructions: Json
          nutrition_per_serving: Json
          image_url: string | null
          created_at: string
        }
        Insert: {
          id?: string
          name: string
          description?: string | null
          prep_time_minutes: number
          cook_time_minutes: number
          servings: number
          difficulty_level: string
          cuisine_type: string
          dietary_tags?: string[]
          ingredients: Json
          instructions: Json
          nutrition_per_serving: Json
          image_url?: string | null
          created_at?: string
        }
        Update: {
          id?: string
          name?: string
          description?: string | null
          prep_time_minutes?: number
          cook_time_minutes?: number
          servings?: number
          difficulty_level?: string
          cuisine_type?: string
          dietary_tags?: string[]
          ingredients?: Json
          instructions?: Json
          nutrition_per_serving?: Json
          image_url?: string | null
          created_at?: string
        }
        Relationships: []
      }
      exercises: {
        Row: {
          id: string
          name: string
          description: string | null
          muscle_groups: string[]
          equipment_required: string[]
          difficulty_level: string
          exercise_type: string
          instructions: Json
          video_url: string | null
          thumbnail_url: string | null
          safety_tips: string[]
          common_mistakes: string[]
          created_at: string
        }
        Insert: {
          id?: string
          name: string
          description?: string | null
          muscle_groups: string[]
          equipment_required?: string[]
          difficulty_level: string
          exercise_type: string
          instructions: Json
          video_url?: string | null
          thumbnail_url?: string | null
          safety_tips?: string[]
          common_mistakes?: string[]
          created_at?: string
        }
        Update: {
          id?: string
          name?: string
          description?: string | null
          muscle_groups?: string[]
          equipment_required?: string[]
          difficulty_level?: string
          exercise_type?: string
          instructions?: Json
          video_url?: string | null
          thumbnail_url?: string | null
          safety_tips?: string[]
          common_mistakes?: string[]
          created_at?: string
        }
        Relationships: []
      }
      meal_plans: {
        Row: {
          id: string
          user_id: string
          name: string
          start_date: string
          end_date: string
          daily_calories: number
          macro_protein_percent: number
          macro_carb_percent: number
          macro_fat_percent: number
          is_active: boolean
          created_by_ai: boolean
          created_at: string
        }
        Insert: {
          id?: string
          user_id: string
          name: string
          start_date: string
          end_date: string
          daily_calories: number
          macro_protein_percent: number
          macro_carb_percent: number
          macro_fat_percent: number
          is_active?: boolean
          created_by_ai?: boolean
          created_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          name?: string
          start_date?: string
          end_date?: string
          daily_calories?: number
          macro_protein_percent?: number
          macro_carb_percent?: number
          macro_fat_percent?: number
          is_active?: boolean
          created_by_ai?: boolean
          created_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "meal_plans_user_id_fkey"
            columns: ["user_id"]
            referencedRelation: "users"
            referencedColumns: ["id"]
          }
        ]
      }
      workout_plans: {
        Row: {
          id: string
          user_id: string
          name: string
          description: string | null
          duration_weeks: number
          difficulty_level: string
          primary_goal: string
          equipment_required: string[]
          is_active: boolean
          created_by_ai: boolean
          created_at: string
        }
        Insert: {
          id?: string
          user_id: string
          name: string
          description?: string | null
          duration_weeks: number
          difficulty_level: string
          primary_goal: string
          equipment_required?: string[]
          is_active?: boolean
          created_by_ai?: boolean
          created_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          name?: string
          description?: string | null
          duration_weeks?: number
          difficulty_level?: string
          primary_goal?: string
          equipment_required?: string[]
          is_active?: boolean
          created_by_ai?: boolean
          created_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "workout_plans_user_id_fkey"
            columns: ["user_id"]
            referencedRelation: "users"
            referencedColumns: ["id"]
          }
        ]
      }
      progress_tracking: {
        Row: {
          id: string
          user_id: string
          metric_type: string
          value: number
          unit: string
          recorded_date: string
          notes: string | null
          created_at: string
        }
        Insert: {
          id?: string
          user_id: string
          metric_type: string
          value: number
          unit: string
          recorded_date: string
          notes?: string | null
          created_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          metric_type?: string
          value?: number
          unit?: string
          recorded_date?: string
          notes?: string | null
          created_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "progress_tracking_user_id_fkey"
            columns: ["user_id"]
            referencedRelation: "users"
            referencedColumns: ["id"]
          }
        ]
      }
      ai_recommendations: {
        Row: {
          id: string
          user_id: string
          recommendation_type: string
          content: Json
          confidence_score: number
          model_version: string
          feedback_rating: number | null
          was_accepted: boolean | null
          created_at: string
        }
        Insert: {
          id?: string
          user_id: string
          recommendation_type: string
          content: Json
          confidence_score: number
          model_version: string
          feedback_rating?: number | null
          was_accepted?: boolean | null
          created_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          recommendation_type?: string
          content?: Json
          confidence_score?: number
          model_version?: string
          feedback_rating?: number | null
          was_accepted?: boolean | null
          created_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "ai_recommendations_user_id_fkey"
            columns: ["user_id"]
            referencedRelation: "users"
            referencedColumns: ["id"]
          }
        ]
      }
      grocery_lists: {
        Row: {
          id: string
          user_id: string
          meal_plan_id: string | null
          name: string
          items: Json
          total_estimated_cost: number | null
          store_preferences: string[]
          is_completed: boolean
          created_at: string
        }
        Insert: {
          id?: string
          user_id: string
          meal_plan_id?: string | null
          name: string
          items: Json
          total_estimated_cost?: number | null
          store_preferences?: string[]
          is_completed?: boolean
          created_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          meal_plan_id?: string | null
          name?: string
          items?: Json
          total_estimated_cost?: number | null
          store_preferences?: string[]
          is_completed?: boolean
          created_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "grocery_lists_user_id_fkey"
            columns: ["user_id"]
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "grocery_lists_meal_plan_id_fkey"
            columns: ["meal_plan_id"]
            referencedRelation: "meal_plans"
            referencedColumns: ["id"]
          }
        ]
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}
