{"name": "@platemotion/database", "version": "0.1.0", "private": true, "main": "./src/index.ts", "types": "./src/index.ts", "scripts": {"lint": "eslint . --max-warnings 0", "type-check": "tsc --noEmit", "generate": "supabase gen types typescript --local > src/types.ts"}, "dependencies": {"@supabase/supabase-js": "catalog:", "@platemotion/shared": "workspace:*"}, "devDependencies": {"@platemotion/eslint-config": "workspace:*", "@platemotion/typescript-config": "workspace:*", "typescript": "catalog:"}}