// App constants
export const APP_NAME = 'PlateMotion'
export const APP_VERSION = '0.1.0'

// User constants
export const SUBSCRIPTION_TIERS = {
  FREE: 'free',
  PREMIUM: 'premium',
  PRO: 'pro'
} as const

export const ACTIVITY_LEVELS = {
  SEDENTARY: 'sedentary',
  LIGHTLY_ACTIVE: 'lightly_active',
  MODERATELY_ACTIVE: 'moderately_active',
  VERY_ACTIVE: 'very_active',
  EXTREMELY_ACTIVE: 'extremely_active'
} as const

export const FITNESS_GOALS = {
  WEIGHT_LOSS: 'weight_loss',
  MUSCLE_GAIN: 'muscle_gain',
  MAINTENANCE: 'maintenance',
  ENDURANCE: 'endurance',
  GENERAL_HEALTH: 'general_health'
} as const

export const FITNESS_EXPERIENCE_LEVELS = {
  BEGINNER: 'beginner',
  INTERMEDIATE: 'intermediate',
  ADVANCED: 'advanced'
} as const

// Nutrition constants
export const DIETARY_RESTRICTIONS = [
  'vegetarian',
  'vegan',
  'pescatarian',
  'keto',
  'paleo',
  'mediterranean',
  'low_carb',
  'low_fat',
  'gluten_free',
  'dairy_free',
  'nut_free',
  'soy_free',
  'egg_free',
  'shellfish_free',
  'halal',
  'kosher'
] as const

export const COMMON_ALLERGIES = [
  'peanuts',
  'tree_nuts',
  'milk',
  'eggs',
  'wheat',
  'soy',
  'fish',
  'shellfish',
  'sesame'
] as const

export const MEAL_TYPES = {
  BREAKFAST: 'breakfast',
  LUNCH: 'lunch',
  DINNER: 'dinner',
  SNACK: 'snack'
} as const

export const DIFFICULTY_LEVELS = {
  EASY: 'easy',
  MEDIUM: 'medium',
  HARD: 'hard'
} as const

// Fitness constants
export const MUSCLE_GROUPS = [
  'chest',
  'back',
  'shoulders',
  'biceps',
  'triceps',
  'forearms',
  'abs',
  'obliques',
  'quadriceps',
  'hamstrings',
  'glutes',
  'calves',
  'full_body'
] as const

export const EQUIPMENT_TYPES = [
  'bodyweight',
  'dumbbells',
  'barbell',
  'resistance_bands',
  'kettlebell',
  'pull_up_bar',
  'yoga_mat',
  'stability_ball',
  'foam_roller',
  'bench',
  'cable_machine',
  'treadmill',
  'stationary_bike',
  'rowing_machine'
] as const

export const EXERCISE_TYPES = {
  STRENGTH: 'strength',
  CARDIO: 'cardio',
  FLEXIBILITY: 'flexibility',
  BALANCE: 'balance',
  PLYOMETRIC: 'plyometric'
} as const

// Progress tracking constants
export const PROGRESS_METRICS = {
  WEIGHT: 'weight',
  BODY_FAT: 'body_fat',
  MUSCLE_MASS: 'muscle_mass',
  MEASUREMENTS: 'measurements',
  PERFORMANCE: 'performance'
} as const

// AI constants
export const AI_MODELS = {
  GEMINI_FLASH: 'gemini-2.5-flash',
  GEMINI_PRO: 'gemini-2.5-pro'
} as const

export const RECOMMENDATION_TYPES = {
  MEAL_PLAN: 'meal_plan',
  WORKOUT_PLAN: 'workout_plan',
  RECIPE: 'recipe',
  EXERCISE: 'exercise',
  ADJUSTMENT: 'adjustment'
} as const

// API constants
export const API_ENDPOINTS = {
  AUTH: '/auth',
  USERS: '/users',
  PROFILES: '/profiles',
  MEAL_PLANS: '/meal-plans',
  RECIPES: '/recipes',
  WORKOUT_PLANS: '/workout-plans',
  EXERCISES: '/exercises',
  PROGRESS: '/progress',
  AI: '/ai',
  GROCERY_LISTS: '/grocery-lists'
} as const

export const HTTP_STATUS = {
  OK: 200,
  CREATED: 201,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  INTERNAL_SERVER_ERROR: 500
} as const

// Validation constants
export const VALIDATION_RULES = {
  PASSWORD_MIN_LENGTH: 8,
  NAME_MAX_LENGTH: 100,
  EMAIL_MAX_LENGTH: 255,
  DESCRIPTION_MAX_LENGTH: 1000,
  MIN_AGE: 13,
  MAX_AGE: 120,
  MIN_HEIGHT_CM: 100,
  MAX_HEIGHT_CM: 250,
  MIN_WEIGHT_KG: 30,
  MAX_WEIGHT_KG: 300
} as const

// Time constants
export const TIME_FORMATS = {
  DATE: 'yyyy-MM-dd',
  DATETIME: 'yyyy-MM-dd HH:mm:ss',
  TIME: 'HH:mm'
} as const

// Storage constants
export const STORAGE_BUCKETS = {
  AVATARS: 'avatars',
  RECIPE_IMAGES: 'recipe-images',
  EXERCISE_VIDEOS: 'exercise-videos',
  PROGRESS_PHOTOS: 'progress-photos'
} as const

// Notification constants
export const NOTIFICATION_TYPES = {
  WORKOUT_REMINDER: 'workout_reminder',
  MEAL_REMINDER: 'meal_reminder',
  PROGRESS_UPDATE: 'progress_update',
  ACHIEVEMENT: 'achievement',
  SYSTEM: 'system'
} as const
