# PlateMotion Setup Guide

This guide will help you set up the PlateMotion monorepo for development.

## Prerequisites

Before you begin, ensure you have the following installed:

- **Node.js** (v18 or higher)
- **pnpm** (v8 or higher) - `npm install -g pnpm`
- **Git**
- **Supabase CLI** - `npm install -g supabase`
- **Expo CLI** - `npm install -g @expo/cli`

## Quick Start

### 1. Clone and Install Dependencies

```bash
# Clone the repository
git clone <repository-url>
cd platemotion

# Install dependencies
pnpm install
```

### 2. Environment Setup

```bash
# Copy environment variables
cp .env.example .env.local

# Edit the environment variables
# You'll need to add your Supabase and Gemini API keys
```

### 3. Supabase Setup

```bash
# Start local Supabase
supabase start

# This will output your local Supabase credentials
# Copy these to your .env.local file
```

### 4. Database Setup

```bash
# Generate database types
pnpm db:generate

# Apply migrations (if any)
supabase db push
```

### 5. Start Development

```bash
# Start all applications
pnpm dev

# Or start specific apps
pnpm dev --filter=mobile
pnpm dev --filter=admin
```

## Detailed Setup Instructions

### Supabase Configuration

1. **Create a Supabase Project**
   - Go to [supabase.com](https://supabase.com)
   - Create a new project
   - Note your project URL and anon key

2. **Local Development**
   ```bash
   # Initialize Supabase locally
   supabase init
   
   # Start local services
   supabase start
   
   # Link to your remote project (optional)
   supabase link --project-ref your-project-ref
   ```

3. **Database Schema**
   ```bash
   # Create initial migration
   supabase db diff --file initial_schema
   
   # Apply to local database
   supabase db reset
   
   # Generate TypeScript types
   pnpm db:generate
   ```

### Google Gemini AI Setup

1. **Get API Key**
   - Go to [Google AI Studio](https://makersuite.google.com/app/apikey)
   - Create a new API key
   - Add it to your `.env.local` file

2. **Test Connection**
   ```bash
   # Test AI integration (once implemented)
   pnpm test --filter=ai
   ```

### Mobile App Setup (Expo)

1. **Install Expo CLI**
   ```bash
   npm install -g @expo/cli
   ```

2. **Start Development**
   ```bash
   cd apps/mobile
   pnpm dev
   
   # Scan QR code with Expo Go app
   # Or press 'i' for iOS simulator, 'a' for Android emulator
   ```

3. **Build for Production**
   ```bash
   # Install EAS CLI
   npm install -g eas-cli
   
   # Configure EAS
   eas build:configure
   
   # Build for iOS/Android
   eas build --platform ios
   eas build --platform android
   ```

### Admin Panel Setup (Next.js)

1. **Start Development**
   ```bash
   cd apps/admin
   pnpm dev
   
   # Open http://localhost:3001
   ```

2. **Build for Production**
   ```bash
   pnpm build
   pnpm start
   ```

## Development Workflow

### Daily Development

```bash
# Start local Supabase
supabase start

# Start all apps
pnpm dev

# In separate terminals, you can also run:
pnpm dev --filter=mobile    # Mobile app only
pnpm dev --filter=admin     # Admin panel only
```

### Making Database Changes

```bash
# Make changes to your database schema
# Then create a migration
supabase db diff --file your_migration_name

# Apply migration locally
supabase db reset

# Generate new types
pnpm db:generate

# Push to remote (when ready)
supabase db push
```

### Adding New Packages

```bash
# Add to specific app
pnpm add package-name --filter=mobile
pnpm add package-name --filter=admin

# Add to shared package
pnpm add package-name --filter=@platemotion/shared

# Add to workspace root
pnpm add -w package-name
```

### Running Tests

```bash
# Run all tests
pnpm test

# Run tests for specific package
pnpm test --filter=shared
pnpm test --filter=ai

# Run tests in watch mode
pnpm test --watch
```

### Code Quality

```bash
# Lint all code
pnpm lint

# Format all code
pnpm format

# Type check all code
pnpm type-check
```

## Project Structure

```
platemotion/
├── apps/
│   ├── mobile/           # Expo React Native app
│   └── admin/            # Next.js admin panel
├── packages/
│   ├── shared/           # Shared utilities and types
│   ├── database/         # Supabase client and types
│   ├── ai/               # Gemini AI integration
│   ├── ui/               # Shared UI components
│   └── config/           # Shared configurations
├── supabase/
│   ├── migrations/       # Database migrations
│   ├── functions/        # Edge functions
│   └── config.toml       # Supabase configuration
└── docs/                 # Documentation
```

## Available Scripts

### Root Level Scripts

- `pnpm dev` - Start all apps in development mode
- `pnpm build` - Build all apps for production
- `pnpm lint` - Lint all code
- `pnpm test` - Run all tests
- `pnpm type-check` - Type check all TypeScript
- `pnpm clean` - Clean all build artifacts
- `pnpm format` - Format all code with Prettier

### Database Scripts

- `pnpm db:start` - Start local Supabase
- `pnpm db:stop` - Stop local Supabase
- `pnpm db:reset` - Reset local database
- `pnpm db:generate` - Generate TypeScript types from database

## Troubleshooting

### Common Issues

1. **Port Conflicts**
   ```bash
   # If ports are in use, you can change them:
   # Mobile: expo start --port 8081
   # Admin: next dev --port 3002
   ```

2. **Supabase Connection Issues**
   ```bash
   # Check Supabase status
   supabase status
   
   # Restart Supabase
   supabase stop
   supabase start
   ```

3. **Type Errors After Database Changes**
   ```bash
   # Regenerate database types
   pnpm db:generate
   
   # Restart TypeScript server in your editor
   ```

4. **Package Installation Issues**
   ```bash
   # Clear node_modules and reinstall
   pnpm clean
   rm -rf node_modules
   pnpm install
   ```

### Getting Help

- Check the [documentation](./README.md)
- Look at existing issues in the repository
- Ask questions in the team chat
- Review the [troubleshooting guide](./troubleshooting.md)

## Next Steps

Once you have the development environment set up:

1. **Review the codebase structure**
2. **Read the [features specification](./features-specification.md)**
3. **Check out the [development roadmap](./development-roadmap.md)**
4. **Start with the MVP features**
5. **Set up your development workflow**

Happy coding! 🚀
