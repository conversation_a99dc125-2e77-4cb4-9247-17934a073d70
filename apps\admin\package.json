{"name": "@platemotion/admin", "version": "0.1.0", "private": true, "scripts": {"build": "next build", "dev": "next dev --port 3001", "lint": "next lint", "start": "next start", "type-check": "tsc --noEmit", "test": "vitest"}, "dependencies": {"next": "catalog:", "react": "catalog:", "react-dom": "catalog:", "@radix-ui/react-slot": "catalog:", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.1.5", "class-variance-authority": "catalog:", "clsx": "catalog:", "tailwind-merge": "catalog:", "lucide-react": "catalog:", "tailwindcss": "catalog:", "zustand": "catalog:", "react-hook-form": "catalog:", "@hookform/resolvers": "catalog:", "zod": "catalog:", "recharts": "catalog:", "@tremor/react": "catalog:", "@tanstack/react-table": "catalog:", "date-fns": "catalog:", "@supabase/supabase-js": "catalog:", "@supabase/auth-helpers-nextjs": "catalog:", "@platemotion/shared": "workspace:*", "@platemotion/database": "workspace:*", "@platemotion/ai": "workspace:*", "@platemotion/ui": "workspace:*"}, "devDependencies": {"@next/eslint-config-next": "^14.0.4", "@types/node": "catalog:", "@types/react": "catalog:", "@types/react-dom": "catalog:", "@platemotion/eslint-config": "workspace:*", "@platemotion/typescript-config": "workspace:*", "autoprefixer": "^10.4.16", "eslint": "catalog:", "postcss": "^8.4.32", "tailwindcss": "catalog:", "typescript": "catalog:", "vitest": "catalog:"}}