{"name": "@platemotion/ai", "version": "0.1.0", "private": true, "main": "./src/index.ts", "types": "./src/index.ts", "scripts": {"lint": "eslint . --max-warnings 0", "type-check": "tsc --noEmit", "test": "vitest"}, "dependencies": {"@google/generative-ai": "catalog:", "ai": "catalog:", "zod": "catalog:", "@platemotion/shared": "workspace:*", "@platemotion/database": "workspace:*"}, "devDependencies": {"@platemotion/eslint-config": "workspace:*", "@platemotion/typescript-config": "workspace:*", "typescript": "catalog:", "vitest": "catalog:"}}